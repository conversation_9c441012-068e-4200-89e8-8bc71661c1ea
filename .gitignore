# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.py~

# Distribution / packaging
.Python
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
tags
MANIFEST

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Dependency directories
node_modules/
jspm_packages/

# IDEs and editors
.vscode/
.vs/
.idea/
.kdev4/
*.kdev4
*.DS_Store
*.swp
*.comp.js
.wnf-lang-status
*debug.log

# Helix Editor
.helix/

# Aider AI Chat
.aider*

# claude
.claude/
CLAUDE.md

# Logs and temporary files
*.log
logs/
debug_*.py
test_*.py

# Build artifacts
build/
client_packages/*/build/

