exclude: 'node_modules|.git'
default_stages: [pre-commit]
fail_fast: false


repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        files: "frappe_assistant_core.*"
        exclude: ".*json$|.*txt$|.*csv|.*md|.*svg"
      - id: check-merge-conflict
      - id: check-ast
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: debug-statements

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.1
    hooks:
      - id: ruff
        name: "Run ruff import sorter"
        args: ["--select=I", "--fix"]

      - id: ruff
        name: "Run ruff linter"
        args: ["--ignore=F403"]

      - id: ruff-format
        name: "Run ruff formatter"

  # Prettier disabled - no JavaScript/CSS files in this project
  # - repo: https://github.com/pre-commit/mirrors-prettier
  #   rev: v2.7.1
  #   hooks:
  #     - id: prettier
  #       types_or: [javascript, vue, scss]


  # ESLint disabled - no JavaScript files in this project
  # - repo: https://github.com/pre-commit/mirrors-eslint
  #   rev: v8.44.0
  #   hooks:
  #     - id: eslint
  #       types_or: [javascript]
  #       args: ['--quiet']

ci:
    autoupdate_schedule: weekly
    skip: []
    submodules: false
