[build-system]
requires = ["setuptools>=64", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "frappe_assistant_core"
version = "2.1.1"
description = "AI-powered ERP assistant with comprehensive MCP integration for Frappe framework"
readme = "README.md"
license = {text = "AGPL-3.0-or-later"}
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: GNU Affero General Public License v3 or later (AGPLv3+)",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Framework :: Frappe",
    "Topic :: Office/Business :: Enterprise Resource Planning (ERP)",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
keywords = ["frappe", "erp", "ai", "assistant", "mcp", "analysis", "business-intelligence"]
requires-python = ">=3.8"
dependencies = [
    # Core data science libraries
    "pandas>=1.3.0",
    "numpy>=1.20.0",
    "matplotlib>=3.4.0",
    "seaborn>=0.11.0",
    "plotly>=5.0.0",
    "scipy>=1.7.0",
    "openpyxl>=3.0.0",  # Excel support for pandas
    # Additional analysis libraries
    "scikit-learn>=1.0.0",
    "bokeh>=2.4.0",
    "altair>=4.1.0",
    "sympy>=1.8.0",
    "networkx>=2.6.0",
    "xlsxwriter>=3.0.0",
    # Network and API dependencies
    "requests>=2.25.0",
    "jsonschema>=4.0.0",
    # SSE Bridge dependencies
    "fastapi>=0.100.0",
    "uvicorn>=0.20.0",
    "python-dotenv>=1.0.0",
    "python-multipart>=0.0.6",
    "httpx>=0.24.0",
    # File processing dependencies
    "pypdf>=3.0.0",
    "Pillow>=10.0.0",
    "python-docx>=0.8.11",
    "pytesseract>=0.3.10",
    "chardet~=5.1.0",  # Compatible with frappe ~=5.1.0 requirement
    # Advanced file processing
    "pdfplumber>=0.9.0",
    "pymupdf>=1.23.0",
    "python-magic>=0.4.27",
    "beautifulsoup4~=4.12.2",  # Compatible with frappe ~=4.12.2 requirement
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.12",
    "black>=21.0",
    "flake8>=3.9",
    "mypy>=0.910",
]
# All other dependencies have been moved to main dependencies
# to ensure they are installed automatically on all servers

[project.urls]
Homepage = "https://github.com/buildswithpaul/Frappe_Assistant_Core"
Documentation = "https://github.com/buildswithpaul/Frappe_Assistant_Core/wiki"
Repository = "https://github.com/buildswithpaul/Frappe_Assistant_Core.git"
"Bug Tracker" = "https://github.com/buildswithpaul/Frappe_Assistant_Core/issues"

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
include = ["frappe_assistant_core*"]

[tool.setuptools.package-data]
frappe_assistant_core = [
    "templates/*.html",
    "public/**/*",
    "*.json",
    "**/*.json",
]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
ignore_missing_imports = true
check_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=frappe_assistant_core --cov-report=html --cov-report=term-missing"

[tool.ruff]
line-length = 110
target-version = "py38"
exclude = [
    "docs/templates/",
    "*.template.py",
    "__pycache__",
    ".git",
    ".mypy_cache",
    ".pytest_cache",
    ".ruff_cache",
    "build",
    "dist",
    "*.egg-info",
]

[tool.ruff.lint]
select = [
    "F",  # pyflakes
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "I",  # isort
    "UP", # pyupgrade
    "B",  # flake8-bugbear
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "B023",  # function definition does not bind loop variable
    "B904",  # raise from
    "UP007", # use X | Y for type annotations
    "F401",  # imported but unused
    "F841",  # local variable assigned but never used
]

[tool.ruff.lint.per-file-ignores]
"frappe_assistant_core/plugins/base_plugin.py" = ["B027"]
