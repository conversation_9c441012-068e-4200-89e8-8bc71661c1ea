# Frappe Assistant Core - ChatGPT OAuth Integration
# Copyright (C) 2025 <PERSON>
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.

"""
OAuth endpoints for ChatGPT Custom Connector integration.
Provides secure authentication flow for ChatGPT to access Frappe Assistant Core.
"""

import json
import secrets
import time
from typing import Any, Dict, Optional

import frappe
from frappe import _
from frappe.utils import cint, get_url


@frappe.whitelist(allow_guest=True)
def authorize():
    """
    OAuth authorization endpoint for ChatGPT.
    Redirects user to login and consent page.
    """
    try:
        # Get OAuth parameters from query string
        client_id = frappe.form_dict.get("client_id")
        redirect_uri = frappe.form_dict.get("redirect_uri")
        response_type = frappe.form_dict.get("response_type", "code")
        state = frappe.form_dict.get("state")
        scope = frappe.form_dict.get("scope", "read")

        # Validate required parameters
        if not client_id or not redirect_uri:
            frappe.throw(_("Missing required OAuth parameters"))

        if response_type != "code":
            frappe.throw(_("Unsupported response_type. Only 'code' is supported."))

        # Validate client_id (should be "chatgpt" or configured value)
        settings = frappe.get_single("Assistant Core Settings")
        allowed_client_id = getattr(settings, "chatgpt_client_id", "chatgpt")
        
        if client_id != allowed_client_id:
            frappe.throw(_("Invalid client_id"))

        # Check if user is logged in
        if frappe.session.user == "Guest":
            # Redirect to login with return URL
            login_url = f"/login?redirect-to=/api/method/frappe_assistant_core.api.chatgpt_oauth.authorize"
            login_url += f"?client_id={client_id}&redirect_uri={redirect_uri}&response_type={response_type}"
            if state:
                login_url += f"&state={state}"
            if scope:
                login_url += f"&scope={scope}"
            
            frappe.local.response["type"] = "redirect"
            frappe.local.response["location"] = login_url
            return

        # Check if user has assistant access
        if not _check_assistant_enabled(frappe.session.user):
            frappe.throw(_("Assistant access is disabled for your account"))

        # Generate authorization code
        auth_code = secrets.token_urlsafe(32)
        
        # Store authorization code with expiration (5 minutes)
        auth_data = {
            "user": frappe.session.user,
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "scope": scope,
            "expires_at": time.time() + 300,  # 5 minutes
            "used": False
        }
        
        frappe.cache().set_value(f"oauth_code:{auth_code}", auth_data, expires_in_sec=300)

        # Redirect back to ChatGPT with authorization code
        callback_url = f"{redirect_uri}?code={auth_code}"
        if state:
            callback_url += f"&state={state}"

        frappe.local.response["type"] = "redirect"
        frappe.local.response["location"] = callback_url

    except Exception as e:
        frappe.log_error(title=_("OAuth Authorization Error"), message=str(e))
        frappe.throw(_("OAuth authorization failed: {0}").format(str(e)))


@frappe.whitelist(allow_guest=True)
def token():
    """
    OAuth token endpoint for ChatGPT.
    Exchanges authorization code for access token.
    """
    try:
        # Get token request parameters
        grant_type = frappe.form_dict.get("grant_type")
        code = frappe.form_dict.get("code")
        redirect_uri = frappe.form_dict.get("redirect_uri")
        client_id = frappe.form_dict.get("client_id")
        client_secret = frappe.form_dict.get("client_secret")

        # Validate grant_type
        if grant_type != "authorization_code":
            return _oauth_error("unsupported_grant_type", "Only authorization_code grant type is supported")

        # Validate required parameters
        if not code or not redirect_uri or not client_id:
            return _oauth_error("invalid_request", "Missing required parameters")

        # Validate client credentials
        settings = frappe.get_single("Assistant Core Settings")
        allowed_client_id = getattr(settings, "chatgpt_client_id", "chatgpt")
        allowed_client_secret = getattr(settings, "chatgpt_client_secret", "")
        
        if client_id != allowed_client_id:
            return _oauth_error("invalid_client", "Invalid client_id")
            
        if client_secret != allowed_client_secret:
            return _oauth_error("invalid_client", "Invalid client_secret")

        # Retrieve and validate authorization code
        auth_data = frappe.cache().get_value(f"oauth_code:{code}")
        if not auth_data:
            return _oauth_error("invalid_grant", "Invalid or expired authorization code")

        # Check if code is expired or already used
        if auth_data.get("used") or time.time() > auth_data.get("expires_at", 0):
            frappe.cache().delete_value(f"oauth_code:{code}")
            return _oauth_error("invalid_grant", "Authorization code expired or already used")

        # Validate redirect_uri matches
        if auth_data.get("redirect_uri") != redirect_uri:
            return _oauth_error("invalid_grant", "Redirect URI mismatch")

        # Mark code as used
        auth_data["used"] = True
        frappe.cache().set_value(f"oauth_code:{code}", auth_data, expires_in_sec=60)

        # Generate access token
        access_token = secrets.token_urlsafe(32)
        refresh_token = secrets.token_urlsafe(32)
        
        # Store access token (expires in 1 hour)
        token_data = {
            "user": auth_data["user"],
            "client_id": client_id,
            "scope": auth_data.get("scope", "read"),
            "expires_at": time.time() + 3600,  # 1 hour
            "refresh_token": refresh_token
        }
        
        frappe.cache().set_value(f"oauth_token:{access_token}", token_data, expires_in_sec=3600)
        frappe.cache().set_value(f"oauth_refresh:{refresh_token}", token_data, expires_in_sec=86400)  # 24 hours

        # Return token response
        return {
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": 3600,
            "refresh_token": refresh_token,
            "scope": auth_data.get("scope", "read")
        }

    except Exception as e:
        frappe.log_error(title=_("OAuth Token Error"), message=str(e))
        return _oauth_error("server_error", "Internal server error")


@frappe.whitelist(allow_guest=True)
def refresh():
    """
    OAuth refresh token endpoint.
    Exchanges refresh token for new access token.
    """
    try:
        grant_type = frappe.form_dict.get("grant_type")
        refresh_token = frappe.form_dict.get("refresh_token")
        client_id = frappe.form_dict.get("client_id")
        client_secret = frappe.form_dict.get("client_secret")

        if grant_type != "refresh_token":
            return _oauth_error("unsupported_grant_type", "Only refresh_token grant type is supported")

        if not refresh_token or not client_id:
            return _oauth_error("invalid_request", "Missing required parameters")

        # Validate client credentials
        settings = frappe.get_single("Assistant Core Settings")
        allowed_client_id = getattr(settings, "chatgpt_client_id", "chatgpt")
        allowed_client_secret = getattr(settings, "chatgpt_client_secret", "")
        
        if client_id != allowed_client_id or client_secret != allowed_client_secret:
            return _oauth_error("invalid_client", "Invalid client credentials")

        # Validate refresh token
        token_data = frappe.cache().get_value(f"oauth_refresh:{refresh_token}")
        if not token_data:
            return _oauth_error("invalid_grant", "Invalid refresh token")

        # Generate new access token
        access_token = secrets.token_urlsafe(32)
        new_refresh_token = secrets.token_urlsafe(32)
        
        # Update token data
        token_data["expires_at"] = time.time() + 3600
        token_data["refresh_token"] = new_refresh_token
        
        # Store new tokens
        frappe.cache().set_value(f"oauth_token:{access_token}", token_data, expires_in_sec=3600)
        frappe.cache().set_value(f"oauth_refresh:{new_refresh_token}", token_data, expires_in_sec=86400)
        
        # Remove old refresh token
        frappe.cache().delete_value(f"oauth_refresh:{refresh_token}")

        return {
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": 3600,
            "refresh_token": new_refresh_token,
            "scope": token_data.get("scope", "read")
        }

    except Exception as e:
        frappe.log_error(title=_("OAuth Refresh Error"), message=str(e))
        return _oauth_error("server_error", "Internal server error")


def validate_oauth_token(authorization_header: str) -> Optional[str]:
    """
    Validate OAuth Bearer token and return user.
    
    Args:
        authorization_header: Authorization header value
        
    Returns:
        Username if valid, None otherwise
    """
    if not authorization_header or not authorization_header.startswith("Bearer "):
        return None
        
    token = authorization_header.split(" ")[1]
    token_data = frappe.cache().get_value(f"oauth_token:{token}")
    
    if not token_data:
        return None
        
    # Check if token is expired
    if time.time() > token_data.get("expires_at", 0):
        frappe.cache().delete_value(f"oauth_token:{token}")
        return None
        
    return token_data.get("user")


def _oauth_error(error_code: str, error_description: str) -> Dict[str, Any]:
    """Format OAuth error response"""
    frappe.local.response["http_status_code"] = 400
    return {
        "error": error_code,
        "error_description": error_description
    }


def _check_assistant_enabled(user: str) -> bool:
    """Check if assistant is enabled for user"""
    try:
        assistant_enabled = frappe.db.get_value("User", user, "assistant_enabled")
        return bool(cint(assistant_enabled)) if assistant_enabled else False
    except Exception:
        return False
