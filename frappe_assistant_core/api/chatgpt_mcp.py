# Frappe Assistant Core - ChatGPT MCP Integration
# Copyright (C) 2025 <PERSON>
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.

"""
ChatGPT-specific MCP endpoint with OAuth authentication support.
This endpoint is optimized for ChatGPT Custom Connector integration.
"""

import json
import traceback
from typing import Any, Dict

import frappe
from frappe import _

from frappe_assistant_core.api.chatgpt_oauth import validate_oauth_token
from frappe_assistant_core.core.tool_registry import get_tool_registry
from frappe_assistant_core.utils.validators import validate_json_rpc


@frappe.whitelist(allow_guest=True)
def handle_chatgpt_mcp_request():
    """
    ChatGPT-specific MCP request handler with OAuth support.
    
    This endpoint is designed specifically for ChatGPT Custom Connectors
    and supports OAuth Bearer token authentication.
    """
    try:
        # Get authorization header
        authorization = frappe.get_request_header("Authorization")
        
        # Validate OAuth token
        user = validate_oauth_token(authorization)
        if not user:
            return _create_error_response(
                None,
                -32000,
                "Authentication required",
                {"error": "Invalid or missing OAuth token", "auth_required": True}
            )

        # Set user session for Frappe permissions
        frappe.set_user(user)

        # Check if user has assistant access enabled
        if not _check_assistant_enabled(user):
            return _create_error_response(
                None,
                -32000,
                f"Assistant access is disabled for user {user}",
                {"user": user, "assistant_enabled": False}
            )

        # Get request body
        request_body = frappe.local.request.get_data(as_text=True)
        if not request_body:
            return _create_error_response(None, -32600, "Invalid Request", "Empty request body")

        # Parse JSON request
        try:
            request = json.loads(request_body)
        except json.JSONDecodeError as e:
            return _create_error_response(None, -32700, "Parse error", f"Invalid JSON: {str(e)}")

        # Validate JSON-RPC structure
        validation_error = validate_json_rpc(request)
        if validation_error:
            return _create_error_response(request.get("id"), -32600, "Invalid Request", validation_error)

        # Route to appropriate handler
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")

        if method == "initialize":
            result = _handle_initialize(params)
        elif method == "tools/list":
            result = _handle_tools_list(params)
        elif method == "tools/call":
            result = _handle_tools_call(params)
        elif method == "ping":
            result = _handle_ping(params)
        else:
            return _create_error_response(request_id, -32601, "Method not found", f"Unknown method: {method}")

        # Return success response
        return _create_success_response(request_id, result)

    except frappe.PermissionError as e:
        return _create_error_response(
            request.get("id") if "request" in locals() else None,
            -32603,
            "Permission denied",
            str(e)
        )
    except Exception as e:
        frappe.log_error(
            title=_("ChatGPT MCP Request Error"),
            message=f"Error processing ChatGPT MCP request: {str(e)}\\n{traceback.format_exc()}"
        )

        return _create_error_response(
            request.get("id") if "request" in locals() else None,
            -32603,
            "Internal error",
            "An internal error occurred"
        )


def _handle_initialize(params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle MCP initialize request for ChatGPT"""
    # Check protocol version
    protocol_version = params.get("protocolVersion", "")
    if not protocol_version:
        raise ValueError("Protocol version is required")

    # Return server capabilities optimized for ChatGPT
    return {
        "protocolVersion": "2025-06-18",
        "capabilities": {
            "tools": {"listChanged": True},
            "resources": {"subscribe": False, "listChanged": False},
            "prompts": {"listChanged": False}
        },
        "serverInfo": {
            "name": "frappe-assistant-core-chatgpt",
            "version": _get_app_version("frappe_assistant_core") or "1.0.0",
            "description": "Frappe Assistant Core integration for ChatGPT"
        }
    }


def _handle_tools_list(params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle MCP tools/list request for ChatGPT"""
    try:
        # Get tool registry
        registry = get_tool_registry()

        # Get available tools for current user
        tools = registry.get_available_tools()

        # Convert to MCP format with ChatGPT optimizations
        mcp_tools = []
        for tool in tools:
            try:
                mcp_tool = tool.to_mcp_format()
                
                # Add ChatGPT-specific optimizations
                if "description" in mcp_tool:
                    # Ensure descriptions are clear for ChatGPT
                    mcp_tool["description"] = _optimize_description_for_chatgpt(mcp_tool["description"])
                
                mcp_tools.append(mcp_tool)
            except Exception as e:
                frappe.logger("chatgpt_mcp").warning(f"Failed to convert tool {tool.name} to MCP format: {str(e)}")

        return {"tools": mcp_tools}

    except Exception as e:
        frappe.log_error(title=_("ChatGPT Tools List Error"), message=f"Error listing tools: {str(e)}")
        raise


def _handle_tools_call(params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle MCP tools/call request for ChatGPT"""
    tool_name = params.get("name")
    arguments = params.get("arguments", {})

    if not tool_name:
        raise ValueError("Tool name is required")

    try:
        # Get tool registry
        registry = get_tool_registry()

        # Get tool instance
        tool = registry.get_tool(tool_name)
        if not tool:
            raise ValueError(f"Tool '{tool_name}' not found")

        # Execute tool
        result = tool._safe_execute(arguments)

        # Convert result to MCP content format optimized for ChatGPT
        if isinstance(result, dict) and "success" in result:
            if result["success"]:
                # Success response
                content = []

                if "data" in result:
                    # Format data for better ChatGPT understanding
                    formatted_data = _format_data_for_chatgpt(result["data"])
                    content.append({"type": "text", "text": formatted_data})
                elif "result" in result:
                    formatted_result = _format_data_for_chatgpt(result["result"])
                    content.append({"type": "text", "text": formatted_result})
                else:
                    # Extract meaningful content
                    filtered_result = {k: v for k, v in result.items() if k not in ["success"]}
                    formatted_result = _format_data_for_chatgpt(filtered_result)
                    content.append({"type": "text", "text": formatted_result})

                return {"content": content}
            else:
                # Tool returned error
                raise ValueError(result.get("error", "Tool execution failed"))
        else:
            # Direct result
            formatted_result = _format_data_for_chatgpt(result)
            content = [{"type": "text", "text": formatted_result}]
            return {"content": content}

    except Exception as e:
        frappe.log_error(
            title=_("ChatGPT Tool Execution Error"),
            message=f"Error executing tool '{tool_name}': {str(e)}"
        )
        raise


def _handle_ping(params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle ping request for ChatGPT connection testing"""
    return {
        "status": "ok",
        "timestamp": frappe.utils.now(),
        "server": "frappe-assistant-core-chatgpt",
        "user": frappe.session.user
    }


def _optimize_description_for_chatgpt(description: str) -> str:
    """Optimize tool descriptions for better ChatGPT understanding"""
    # Add context about Frappe/ERPNext if not present
    if "frappe" not in description.lower() and "erpnext" not in description.lower():
        description = f"[ERPNext/Frappe] {description}"
    
    # Ensure descriptions are action-oriented
    if not any(word in description.lower() for word in ["create", "get", "list", "update", "delete", "search", "generate"]):
        description = f"Tool to {description.lower()}"
    
    return description


def _format_data_for_chatgpt(data: Any) -> str:
    """Format data for optimal ChatGPT understanding"""
    if isinstance(data, dict):
        # For dictionaries, create a more readable format
        if len(data) == 1 and "message" in data:
            return str(data["message"])
        elif "name" in data and "doctype" in data:
            # Frappe document format
            return f"Document: {data['doctype']} - {data['name']}\\n" + json.dumps(data, indent=2, default=str)
        else:
            return json.dumps(data, indent=2, default=str)
    elif isinstance(data, list):
        if len(data) == 0:
            return "No results found."
        elif len(data) == 1:
            return _format_data_for_chatgpt(data[0])
        else:
            return f"Found {len(data)} results:\\n" + json.dumps(data, indent=2, default=str)
    else:
        return str(data)


def _create_success_response(request_id: Any, result: Dict[str, Any]) -> str:
    """Create JSON-RPC 2.0 success response"""
    response = {"jsonrpc": "2.0", "result": result, "id": request_id}
    return json.dumps(response)


def _create_error_response(request_id: Any, code: int, message: str, data: Any = None) -> str:
    """Create JSON-RPC 2.0 error response"""
    error = {"code": code, "message": message}

    if data is not None:
        error["data"] = data

    response = {"jsonrpc": "2.0", "error": error, "id": request_id}
    return json.dumps(response)


def _get_app_version(app_name: str) -> str:
    """Get version of a Frappe app"""
    try:
        if app_name == "frappe_assistant_core":
            from frappe_assistant_core import __version__
            return __version__
        else:
            module = __import__(app_name)
            version = getattr(module, "__version__", None)
            return version if version else ""
    except ImportError:
        return ""


def _check_assistant_enabled(user: str) -> bool:
    """Check if assistant is enabled for user"""
    try:
        assistant_enabled = frappe.db.get_value("User", user, "assistant_enabled")
        return bool(frappe.utils.cint(assistant_enabled)) if assistant_enabled else False
    except Exception:
        return False
