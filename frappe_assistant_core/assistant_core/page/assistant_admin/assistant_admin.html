<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>assistant Server Management</title>
    <link rel="stylesheet" href="/assets/frappe_assistant_core/css/assistant_admin.css">
    <script src="/assets/frappe_assistant_core/js/assistant_admin.js"></script>
</head>
<body>
    <div class="container">
        <h1>Assistant Server Management</h1>
        <div id="server-status">
            <h2>Server Status</h2>
            <p id="status-message">Loading...</p>
        </div>
        <div id="settings">
            <h2>Settings</h2>
            <form id="settings-form">
                <label for="server-enabled">Enable assistant Server:</label>
                <input type="checkbox" id="server-enabled" name="server_enabled">

                <label for="max-connections">Max Connections:</label>
                <input type="number" id="max-connections" name="max_connections" min="1">

                <label for="authentication-required">Require Authentication:</label>
                <input type="checkbox" id="authentication-required" name="authentication_required">

                <label for="rate-limit">Rate Limit:</label>
                <input type="number" id="rate-limit" name="rate_limit" min="1">

                <label for="allowed-origins">Allowed Origins:</label>
                <textarea id="allowed-origins" name="allowed_origins"></textarea>

                <button type="submit">Save Settings</button>
            </form>
        </div>
        <div id="logs">
            <h2>Audit Logs</h2>
            <table id="logs-table">
                <thead>
                    <tr>
                        <th>Client ID</th>
                        <th>User</th>
                        <th>Connection Type</th>
                        <th>Status</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Logs will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>