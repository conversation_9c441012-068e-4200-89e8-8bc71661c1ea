{"actions": [], "autoname": "naming_series:", "creation": "2025-06-21 22:45:07.030425", "default_view": "List", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "action", "tool_name", "user", "column_break_1", "status", "execution_time", "timestamp", "target_section", "target_doctype", "target_name", "column_break_2", "client_id", "ip_address", "details_section", "input_data", "output_data", "error_message"], "fields": [{"default": "ASST-AUDIT-.YYYY.-.MM.-.DD.-", "fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "ASST-AUDIT-.YYYY.-.MM.-.DD.-"}, {"fieldname": "action", "fieldtype": "Data", "in_list_view": 1, "label": "Action", "length": 120, "read_only": 1, "reqd": 1}, {"fieldname": "tool_name", "fieldtype": "Data", "in_list_view": 1, "label": "Tool Name", "length": 120, "read_only": 1}, {"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Success\nError\nTimeout\nPermission Denied", "read_only": 1, "reqd": 1}, {"fieldname": "execution_time", "fieldtype": "Float", "label": "Execution Time (seconds)", "precision": "3", "read_only": 1}, {"fieldname": "timestamp", "fieldtype": "Datetime", "label": "Timestamp", "read_only": 1, "reqd": 1}, {"collapsible": 1, "fieldname": "target_section", "fieldtype": "Section Break", "label": "Target Information"}, {"fieldname": "target_doctype", "fieldtype": "Data", "label": "Target DocType", "options": "DocType", "read_only": 1}, {"fieldname": "target_name", "fieldtype": "Data", "label": "Target Document", "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "client_id", "fieldtype": "Data", "label": "Client ID", "read_only": 1}, {"fieldname": "ip_address", "fieldtype": "Data", "label": "IP Address", "read_only": 1}, {"collapsible": 1, "fieldname": "details_section", "fieldtype": "Section Break", "label": "Execution Details"}, {"fieldname": "input_data", "fieldtype": "Code", "label": "Input Data", "options": "JSON", "read_only": 1}, {"fieldname": "output_data", "fieldtype": "Code", "label": "Output Data", "options": "JSON", "read_only": 1}, {"fieldname": "error_message", "fieldtype": "Text", "label": "Error Message", "read_only": 1}], "links": [], "modified": "2025-09-09 16:49:34.747302", "modified_by": "Administrator", "module": "Assistant Core", "name": "Assistant <PERSON><PERSON>", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Auditor"}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}